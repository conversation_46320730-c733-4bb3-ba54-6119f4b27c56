import { fileURLToPath, URL } from 'node:url';
import vue from '@vitejs/plugin-vue';
import UnoCSS from 'unocss/vite';
import { defineConfig } from 'vite';

export default defineConfig(({ mode }) => {
  return {
    plugins: [vue(), UnoCSS()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    define: {
      __DEV__: ['dev', 'test', 'development', 'testing', 'local'].includes(mode),
    },
    server: {
      port: 5174,
      proxy: {
        '/api': {
          target: 'https://ai-dev.xiaoxingcloud.com',
          changeOrigin: true,
        },
        '/customIP': {
          target: 'http://************:8000',
          rewrite: (path) => {
            return path.replace('/customIP', '');
          },
        },
      },
    },
  };
});
