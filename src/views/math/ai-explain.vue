<template>
  <div v-if="false" class="h-screen">
    <div class="w-full flex flex-col">
      <div class="container mx-auto box-border px-4 py-6 pt-18">
        <div
          v-for="(item, idx) in tabs" :id="`section-${idx}`" :key="item.name"
          :class="activeTab === idx ? 'mb-10 rounded-lg bg-white p-6 shadow-md' : 'hidden'"
        >
          <h2 class="mb-4 text-xl text-blue-600 font-bold">
            {{ item.name }}
          </h2>
          <MdPreview :model-value="item.content" />
        </div>
      </div>
    </div>
  </div>

  <button v-if="isDevMode" class="fixed right-0 top-0" @click="refreshPage">
    刷新页面
  </button>

  <div class="box-border size-screen flex flex-col overflow-hidden bg-[#EDF4FA] p-[20px]">
    <div class="mb-[16px] flex">
      <Tabs :active-tab="activeTab" :tabs="tabs" @handle-tab="handleTabSelect" />
    </div>

    <!-- AI讲 按钮 -->
    <div
      v-if="tabs[activeTab].isFetchContentCompleted && tabs[activeTab].isShowSpeechBtn && !isAnsweringQuestion && !isHistoryMode && !isUsingMic"
      class="broadcast-btn" @click="speechByText"
    >
      <div v-if="isSpeeching" class="custom-wave-class">
        <AudioWave :bar-num="5" />
      </div>
      <img v-if="!isSpeeching" src="@/assets/broadcast-playing.png" class="size-[20px]">
      <img src="@/assets/broadcast-text.png" class="h-[15px] w-[34px]">
      <!-- <span class="bg-text">AI讲</span> -->
    </div>

    <!-- <span v-if="isConvertingText && !isSpeeching" class="loader" />
    <span v-if="isConvertingText" class="bg-text">思考中，请稍等</span> -->

    <div ref="contentContainerRef" class="h-0 grow overflow-y-auto">
      <!-- <AudioWave /> -->
      <div
        v-if="[0, 1, 2].includes(activeTab)" class="relative flex rounded-[8px]"
        style="clip-path: border-box;"
      >
        <!-- <div class="w-[20px] bg-[#FFA033]" /> -->
        <div class="w-full rounded-[24px] bg-[#D7EDFF] px-[12px] pb-[12px] pt-[18px]">
          <!-- TODO: md 内容样式最后再处理 -->
          <MdPreview v-if="mdContent" class="custom-md-previewer" :model-value="mdContent" />
          <div v-else class="flex items-center justify-center gap-x-[10px]">
            <span class="loader" />
            <span class="text-[#2E2E2E]">思考中，请稍等</span>
          </div>
        </div>
      </div>

      <div v-if="[3].includes(activeTab)" class="relative flex rounded-[8px] pt-[16px]" style="clip-path: border-box;">
        <video
          v-if="videoItem.url" :src="videoItem.url" :poster="videoItem.cover" preload="none" controls
          disablePictureInPicture controlsList="nodownload" class="w-full rounded-[8px]"
        />
      </div>

      <div class="flex flex-col gap-y-[16px] px-[6px] pt-[16px]">
        <template v-for="(item, index) of chatMessageWithTabType[activeTab]" :key="index">
          <div v-if="item.type === 'preset'">
            <div class="flex-inline rounded-[0_24px_24px_24px] bg-[#CBDCFF] p-[12px]">
              <span class="shrink-0 text-[#2E2E2E] font-medium">
                AI：
              </span>

              <div>
                <span class="text-[#2E2E2E] font-normal">{{ item.message }}</span>
              </div>
            </div>
          </div>

          <div v-if="item.type === 'student'">
            <div class="flex-inline rounded-[0_24px_24px_24px] bg-white p-[12px]">
              <span class="shrink-0 text-[#2E2E2E] font-medium">
                学生：
              </span>

              <div>
                <AudioWave v-if="!item.message" />
                <span v-else class="text-[#2E2E2E] font-normal">{{ item.message }}</span>
              </div>
            </div>
          </div>

          <div v-if="item.type === 'ai'" class="flex-inline rounded-[0_24px_24px_24px] bg-[#7083FF] p-[12px]">
            <span class="shrink-0 text-white font-medium">
              AI：
            </span>
            <div>
              <AudioWave v-if="!item.message" />
              <MdPreview v-else :model-value="item.message" style="color: #FFFFFF" />
            </div>
          </div>
        </template>
      </div>
    </div>

    <div v-if="false" class="flex justify-end gap-x-[10px]">
      <div
        class="box-border h-[32px] w-[100px] flex items-center justify-center rounded-[8px] bg-[#D4E8FF] px-[5px] text-[16px] text-[#2E2E2E] font-normal"
        @click="speechByText"
      >
        <span>AI讲</span>
      </div>
      <div
        class="box-border h-[32px] w-[100px] flex items-center justify-center rounded-[8px] bg-[#D4E8FF] px-[5px] text-[16px] text-[#2E2E2E] font-normal"
      >
        <span>有疑问</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDebounceFn, useMutationObserver } from '@vueuse/core';
import { fetchEventData } from 'fetch-sse';
import { MdPreview } from 'md-editor-v3';
import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';
import request from '@/api/http';
import AudioWave from '@/components/AudioWave.vue';
import Tabs from '@/components/tabs.vue';

type ITabType = 'understanding_agent' | 'knowledge_point_agent' | 'solution_steps_agent';

interface FetchDataParams {
  ocr_record_id: string;
  index: number;
  token: string;
  type: ITabType;
}
const router = useRouter();
const contentContainer = useTemplateRef('contentContainerRef');
const isDevMode = ref(__DEV__);
const activeTab = ref(0);
const isGenerating = ref(false);
const isHistoryMode = ref(false);
const fetchDataParams = ref<FetchDataParams>({
  ocr_record_id: '',
  index: 0,
  token: '',
  type: 'understanding_agent',
});
const abortController = ref<AbortController>(new AbortController());
const isPageVisible = ref(false);
const tabs = ref([
  {
    name: '知识点',
    content: '',
    // content: `"给定函数 $$f(x) = \\sin \\left( 2x + \\frac{3\\pi}{2} \\right) - 3\\cos x$$。\n\n我们需要找到该函数的最小值。为此，我们可以使用三角函数的性质和公式进行化简和分析。\n\n### 解析步骤：\n\n1. **化简三角函数**\n   - 首先，应用三角函数的相位移动性质：\n     $$\\sin \\left( 2x + \\frac{3\\pi}{2} \\right) = \\sin \\left( 2x \\right) \\cdot \\cos \\left( \\frac{3\\pi}{2} \\right) + \\cos \\left( 2x \\right) \\cdot \\sin \\left( \\frac{3\\pi}{2} \\right)$$\n     - 因为 $$\\cos \\left( \\frac{3\\pi}{2} \\right) = 0$$ 和 $$\\sin \\left( \\frac{3\\pi}{2} \\right) = -1$$，所以：\n     $$\\sin \\left( 2x + \\frac{3\\pi}{2} \\right) = -\\cos \\left( 2x \\right)$$\n\n2. **重写函数**\n   - 代入化简结果：\n     $$f(x) = -\\cos \\left( 2x \\right) - 3\\cos x$$\n\n3. **使用二倍角公式**\n   - 应用二倍角公式：$$\\cos \\left( 2x \\right) = 2\\cos^2 x - 1$$\n   - 将其代入函数中：\n     $$f(x) = - \\left( 2\\cos^2 x - 1 \\right) - 3\\cos x = -2\\cos^2 x - 3\\cos x + 1$$\n\n4. **转化为关于 $$\\cos x$$ 的二次函数**\n   - 设 $$y = \\cos x$$，则函数变为：\n     $$f(y) = -2y^2 - 3y + 1$$\n   - 我们需要找到该二次函数的最小值。\n\n5. **求二次函数的最小值**\n   - 二次函数 $$f(y) = -2y^2 - 3y + 1$$ 是开口向下的抛物线，其最小值出现在顶点。\n   - 顶点的横坐标为：$$y = -\\frac{b}{2a} = -\\frac{-3}{2 \\times (-2)} = \\frac{3}{4}$$\n   - 将 $$y = \\frac{3}{4}$$ 代入 $$f(y)$$ 中，计算函数值：\n     $$f\\left(\\frac{3}{4}\\right) = -2\\left(\\frac{3}{4}\\right)^2 - 3\\left(\\frac{3}{4}\\right) + 1 = -\\frac{9}{8} - \\frac{9}{4} + 1$$\n     $$= -\\frac{9}{8} - \\frac{18}{8} + \\frac{8}{8}$$\n     $$= -\\frac{19}{8}$$\n\n6. **验证 $$y$$ 的取值范围**\n   - 由于 $$y = \\cos x$$，所以 $$y$$ 的取值范围是 $$\\left[-1, 1\\right]$$。\n   - 检查边界值：\n     - 当 $$y = 1$$，$$f(y) = -2 \\times 1^2 - 3 \\times 1 + 1 = -4$$\n     - 当 $$y = -1$$，$$f(y) = -2 \\times (-1)^2 - 3 \\times (-1) + 1 = 4$$\n\n在边界值 $$y = 1$$ 时，函数值为 $$-4$$，这是 $$f(x)$$ 的最小值。\n\n### 结论：\n\n函数 $$f(x)$$ 的最小值为 $$-4$$。\n\n是否有任何疑问或需要进一步解释的部分？"`,
    disabled: () => isGenerating.value,
    isFetchContentCompleted: false,
    isFetching: false,
    type: 'knowledge_point_agent',
    isShowSpeechBtn: false,
  },
  {
    name: '解题思路',
    content: '',
    // content: `"给定函数 $$f(x) = \\sin \\left( 2x + \\frac{3\\pi}{2} \\right) - 3\\cos x$$。\n\n我们需要找到该函数的最小值。为此，我们可以使用三角函数的性质和公式进行化简和分析。\n\n### 解析步骤：\n\n1. **化简三角函数**\n   - 首先，应用三角函数的相位移动性质：\n     $$\\sin \\left( 2x + \\frac{3\\pi}{2} \\right) = \\sin \\left( 2x \\right) \\cdot \\cos \\left( \\frac{3\\pi}{2} \\right) + \\cos \\left( 2x \\right) \\cdot \\sin \\left( \\frac{3\\pi}{2} \\right)$$\n     - 因为 $$\\cos \\left( \\frac{3\\pi}{2} \\right) = 0$$ 和 $$\\sin \\left( \\frac{3\\pi}{2} \\right) = -1$$，所以：\n     $$\\sin \\left( 2x + \\frac{3\\pi}{2} \\right) = -\\cos \\left( 2x \\right)$$\n\n2. **重写函数**\n   - 代入化简结果：\n     $$f(x) = -\\cos \\left( 2x \\right) - 3\\cos x$$\n\n3. **使用二倍角公式**\n   - 应用二倍角公式：$$\\cos \\left( 2x \\right) = 2\\cos^2 x - 1$$\n   - 将其代入函数中：\n     $$f(x) = - \\left( 2\\cos^2 x - 1 \\right) - 3\\cos x = -2\\cos^2 x - 3\\cos x + 1$$\n\n4. **转化为关于 $$\\cos x$$ 的二次函数**\n   - 设 $$y = \\cos x$$，则函数变为：\n     $$f(y) = -2y^2 - 3y + 1$$\n   - 我们需要找到该二次函数的最小值。\n\n5. **求二次函数的最小值**\n   - 二次函数 $$f(y) = -2y^2 - 3y + 1$$ 是开口向下的抛物线，其最小值出现在顶点。\n   - 顶点的横坐标为：$$y = -\\frac{b}{2a} = -\\frac{-3}{2 \\times (-2)} = \\frac{3}{4}$$\n   - 将 $$y = \\frac{3}{4}$$ 代入 $$f(y)$$ 中，计算函数值：\n     $$f\\left(\\frac{3}{4}\\right) = -2\\left(\\frac{3}{4}\\right)^2 - 3\\left(\\frac{3}{4}\\right) + 1 = -\\frac{9}{8} - \\frac{9}{4} + 1$$\n     $$= -\\frac{9}{8} - \\frac{18}{8} + \\frac{8}{8}$$\n     $$= -\\frac{19}{8}$$\n\n6. **验证 $$y$$ 的取值范围**\n   - 由于 $$y = \\cos x$$，所以 $$y$$ 的取值范围是 $$\\left[-1, 1\\right]$$。\n   - 检查边界值：\n     - 当 $$y = 1$$，$$f(y) = -2 \\times 1^2 - 3 \\times 1 + 1 = -4$$\n     - 当 $$y = -1$$，$$f(y) = -2 \\times (-1)^2 - 3 \\times (-1) + 1 = 4$$\n\n在边界值 $$y = 1$$ 时，函数值为 $$-4$$，这是 $$f(x)$$ 的最小值。\n\n### 结论：\n\n函数 $$f(x)$$ 的最小值为 $$-4$$。\n\n是否有任何疑问或需要进一步解释的部分？"`,
    disabled: () => isGenerating.value,
    isFetchContentCompleted: false,
    isFetching: false,
    type: 'understanding_agent',
    isShowSpeechBtn: true,
  },
  {
    name: '计算过程',
    content: '',
    disabled: () => isGenerating.value,
    isFetchContentCompleted: false,
    isFetching: false,
    type: 'solution_steps_agent',
    isShowSpeechBtn: true,
  },
  {
    name: '课程学习',
    disabled: () => isGenerating.value,
  },
]);

// let authrizationToken = 'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsYXQiOjE3NTMxNzc0OTMsImV4cCI6MTc1Mzc4MjI5MywianRpIjoiYzk5NTVhNjctZjNjMC00NDUzLThmMmYtZDQ4YzZkZjBjM2I4IiwidHlwZSI6ImFjY2VzcyIsInN1YmplY3QiOnsiaWQiOjE0MH19.GtYKMHp53zxZ4NOOM97PMQVmEFfHT06RBdQPrlPX24U';
let authrizationToken = '';
let ocrRecordId = '';
let tabTypeName = '';
let fetchKnowledgeIndex = -1;
// qa 的上下文
let recordId = '';
let isRefreshing = false;
function androidBridgeFetchKnowledge(data: {
  ocr_record_id: string;
  index: number;
  token: string;
  type: ITabType;
  fromHistory: boolean;
}) {
  isPageVisible.value = true;
  resetDefaultState();
  const urlMap = {
    knowledge_point_agent: 0,
    understanding_agent: 1,
    solution_steps_agent: 2,
  };
  const { ocr_record_id, index, token, type, fromHistory } = data;
  console.log('androidBridgeFetchKnowledge', { ocr_record_id, index, token, type });
  // 切题的时候就刷新页面（临时方案）
  if (fetchKnowledgeIndex !== -1 && (fetchKnowledgeIndex !== index)) {
    // 避免重复执行刷新页面导致卡死。
    if (isRefreshing)
      return;

    tabs.value[0].content = '';
    tabs.value[1].content = '';
    tabs.value[2].content = '';
    chatMessageWithTabType.value = {};

    isRefreshing = true;
    refreshPage();
    return;
  }
  if (fromHistory) {
    // window?.AndroidInterface?.onTabSwitch?.(false);
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  }

  authrizationToken = token;
  getPresetPhraseList();
  ocrRecordId = ocr_record_id;
  tabTypeName = type;
  fetchKnowledgeIndex = index;
  console.log(tabs.value[urlMap[type as keyof typeof urlMap]].content);

  if (tabs.value[urlMap[type as keyof typeof urlMap]].content)
    return;
  if (tabs.value[urlMap[type as keyof typeof urlMap]].isFetching)
    return;

  // if (!tabs.value[activeTab.value].isFetchContentCompleted)
  //   return;
  tabs.value.map((item) => {
    item.isFetching = true;
  });

  // window?.AndroidInterface?.onTabSwitch?.(false);
  const originSelectedTab = urlMap[type as keyof typeof urlMap]; ;
  activeTab.value = originSelectedTab;
  fetchDataParams.value = data; // 保存请求数据
  abortController.value = new AbortController();
  try {
    fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/lecture/${type}?ocr_record_id=${ocr_record_id}&index=${index}`, {
      headers: {
        Authorization: token,
      },
      signal: abortController.value.signal,
      onMessage: (event) => {
        if (!event || !event.data)
          return;
        try {
          const data = JSON.parse(event.data);
          if (!recordId)
            recordId = data.record_id;
          // if (__DEV__)
          //   console.log('androidBridgeFetchKnowledge', data?.content);
          switch (type) {
            case 'knowledge_point_agent':
              tabs.value[0].content += data?.content || '';
              break;
            case 'understanding_agent':
              tabs.value[1].content += data?.content || '';
              break;
            case 'solution_steps_agent':
              tabs.value[2].content += data?.content || '';
              break;
            default:
              break;
          }
        }
        catch (e) {
          console.error('Failed to parse SSE message:', e);
        }
      },
      onError: (error) => {
        tabs.value[originSelectedTab].isFetchContentCompleted = false;
        tabs.value[originSelectedTab].isFetching = false;
        if (error.name !== 'AbortError') {
          console.error('Chat error:', error);
        }
      },
      onClose: async () => {
        window?.AndroidInterface?.onReceivedAiAnalysis?.(type);
        tabs.value[originSelectedTab].isFetchContentCompleted = true;
        tabs.value[originSelectedTab].isFetching = false;
        if (!isHistoryMode.value) {
          window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
        }
        // window?.AndroidInterface?.onTabSwitch?.(true);
        await window.MathJax.startup.promise;
        // 使用nextTick
        nextTick(async () => {
          await window.MathJax?.typesetPromise();
        });
        console.log('onClose');
      },
      onOpen: () => {
        console.log('onOpen');
      },
    });
  }
  catch (error) {
    console.error('Chat error:', error);
  }

  // 静默调取其它tab的数据。
  const typeList: ITabType[] = ['knowledge_point_agent', 'understanding_agent', 'solution_steps_agent'];
  const restTypeList = typeList.filter(t => t !== type);
  restTypeList.forEach((restType) => {
    loadContentSilently({
      ocr_record_id,
      index,
      token,
      type: restType,
    });
  });
}

function loadContentSilently(data: {
  ocr_record_id: string;
  index: number;
  token: string;
  type: ITabType;
}) {
  const urlMap = {
    knowledge_point_agent: 0,
    understanding_agent: 1,
    solution_steps_agent: 2,
  };
  const { ocr_record_id, index, token, type } = data;
  const typeIndex = urlMap[type as keyof typeof urlMap];
  try {
    fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/lecture/${type}?ocr_record_id=${ocr_record_id}&index=${index}`, {
      headers: {
        Authorization: token,
      },
      // signal: abortController.value.signal,
      onMessage: (event) => {
        if (!event || !event.data)
          return;
        try {
          const data = JSON.parse(event.data);
          if (!recordId)
            recordId = data.record_id;
          // if (__DEV__)
          //   console.log('loadContentSilently', data?.content);
          switch (type) {
            case 'knowledge_point_agent':
              tabs.value[0].content += data?.content || '';
              break;
            case 'understanding_agent':
              tabs.value[1].content += data?.content || '';
              break;
            case 'solution_steps_agent':
              tabs.value[2].content += data?.content || '';
              break;
            default:
              break;
          }
        }
        catch (e) {
          console.error('Failed to parse SSE message:', e);
        }
      },
      onError: (error) => {
        tabs.value[typeIndex].isFetchContentCompleted = false;
        tabs.value[typeIndex].isFetching = false;
        if (error.name !== 'AbortError') {
          console.error('Chat error:', error);
        }
      },
      onClose: async () => {
        console.log('onClose');
        tabs.value[typeIndex].isFetchContentCompleted = true;
        tabs.value[typeIndex].isFetching = false;
      },
      onOpen: () => {
        console.log('onOpen');
      },
    });
  }
  catch (error) {
    console.error('Chat error:', error);
  }
}

const videoItem = ref({
  url: '',
  cover: '',
});
const videoUrlList = ref([]);
function handleTabSelect(index: number) {
  console.log(index);

  if (activeTab.value === index)
    return;

  window?.AndroidInterface?.onTabSwitch?.(true);
  console.log('onTabSwitch');
  isSpeeching.value = false;

  activeTab.value = index;
  // 课程学习不处理。
  if (index === 3) {
    if (!isHistoryMode.value) {
      window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    }
    if (!videoUrlList.value.length) {
      request.get('/api/v1/math/config', {
        params: {
          key: 'temp_videos',
        },
        headers: {
          Authorization: authrizationToken,
        },
      })
        .then((res) => {
          console.log('随机视频', res.data);
          videoUrlList.value = res.data.data || [];
          const randomIndex = Math.floor(Math.random() * videoUrlList.value.length);
          if (videoUrlList.value[randomIndex]) {
            videoItem.value = videoUrlList.value[randomIndex];
          }
          console.log('随机 randomIndex 1', randomIndex, videoItem.value);
        });
    }
    else {
      const randomIndex = Math.floor(Math.random() * videoUrlList.value.length);
      if (videoUrlList.value[randomIndex]) {
        videoItem.value = videoUrlList.value[randomIndex];
        console.log('随机 randomIndex 2', randomIndex, videoItem.value);
      }
    }
    return;
  }
  else {
    if (!isHistoryMode.value) {
      window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
    }
  }

  const typeMapping: (ITabType)[] = ['knowledge_point_agent', 'understanding_agent', 'solution_steps_agent'];

  if (isHistoryMode.value) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  }
  else {
    if (tabs.value[activeTab.value].isFetchContentCompleted) {
      window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
    }
    if (!tabs.value[index].content) {
      androidBridgeFetchKnowledge({
        ocr_record_id: ocrRecordId,
        index: fetchKnowledgeIndex,
        token: authrizationToken,
        type: typeMapping[index],
        fromHistory: isHistoryMode.value,
      });
    }
  }
}

function androidBridgeFetchKnowledgeHistory(data: {
  understanding_agent: string;
  knowledge_point_agent: string;
  solution_steps_agent: string;
  tabType: ITabType;
  token: string;
}) {
  console.log('androidBridgeFetchKnowledgeHistory', data);
  isPageVisible.value = true;
  authrizationToken = data.token;
  getPresetPhraseList();
  // window?.AndroidInterface?.onTabSwitch?.(false);
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  isHistoryMode.value = true;
  tabs.value[0].content = data.knowledge_point_agent;
  tabs.value[0].isFetchContentCompleted = true;
  tabs.value[1].content = data.understanding_agent;
  tabs.value[1].isFetchContentCompleted = true;
  tabs.value[2].content = data.solution_steps_agent;
  tabs.value[2].isFetchContentCompleted = true;

  const urlMap = {
    knowledge_point_agent: 0,
    understanding_agent: 1,
    solution_steps_agent: 2,
  };

  activeTab.value = urlMap[data.tabType as keyof typeof urlMap];
}

const mdContent = computed(() => {
  return tabs.value[activeTab.value].content;
});

const speechTextCache: { [key in number]: string } = {};
const isSpeeching = ref(false);
const isConvertingText = ref(false);
const isSpeechConverting = ref(false);
let abortConvertingController = new AbortController();
function speechByText() {
  if (isSpeeching.value) {
    console.log('speechByText', isSpeeching.value);
    window?.AndroidInterface?.androidBridgeClearTTS?.();
    androidBridgeAudioPlayFinished();
    return;
  }
  const currentActiveTab = activeTab.value;
  // 如果有缓存就播放缓存
  if (speechTextCache[currentActiveTab]) {
    if (__DEV__)
      console.log({ cache: speechTextCache[currentActiveTab] });
    isSpeeching.value = true;
    window?.AndroidInterface?.androidBridgeBroadcastByText?.(speechTextCache[currentActiveTab]);
    window?.AndroidInterface?.androidBridgeStreamStop?.();
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    // TODO: 这里还需要监听播放完毕的标志。避免用户重复点击。
    return;
  }
  if (!isConvertingText.value) {
    isConvertingText.value = true;
  }
  else {
    return;
  }
  // 整句保存在此。用作缓存
  let tempText = '';
  // 短句保存在此。用作播报
  let trunckText = '';
  const splitSymbol = ['，', '；', '。', '！'];
  // window?.AndroidInterface?.onTabSwitch?.(false);
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  isSpeeching.value = true;
  isSpeechConverting.value = true;
  fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/convert`, {
    // fetchEventData(`http://192.168.8.37:8000/api/v1/math/convert`, {
    method: 'post',
    headers: {
      Authorization: authrizationToken,
    },
    data: {
      text: mdContent.value,
      summary: true,
    },
    onMessage: (e) => {
      if (!e || !e.data)
        return;

      try {
        const data = JSON.parse(e.data);
        const isFinished = data?.finish_reason === 'stop';
        const content = data.content;
        const hasSplitSymbol = content ? splitSymbol.some(symbol => content.includes(symbol)) : false;
        tempText += (content || '');
        trunckText += (content || '');
        if (hasSplitSymbol) {
          if (__DEV__)
            console.log({ trunckText });
          if (trunckText && isPageVisible.value) {
            window?.AndroidInterface?.androidBridgeBroadcastByText?.(trunckText);
          }

          trunckText = '';
        }
        if (isFinished) {
          // 最后一段话可能没有分隔符。需要把最后一个 trunckText 加入到 tempText 中。
          if (trunckText && isPageVisible.value) {
            console.log('有最后一段话', trunckText);
            if (__DEV__)
              console.log({ trunckText });
            window?.AndroidInterface?.androidBridgeBroadcastByText?.(trunckText);
            trunckText = '';
          }

          console.log('convert finished', isFinished);
          // tts 结束告诉 native 停止
          window?.AndroidInterface?.androidBridgeStreamStop?.();

          // 缓存 tempText
          speechTextCache[currentActiveTab] = tempText;
        }
      }
      catch (e) {
        console.error('Failed to parse SSE message:', e);
      }
    },
    onError: (e) => {
      console.log('speechByText error', e);

      isSpeeching.value = false;
      isConvertingText.value = false;
      window?.AndroidInterface?.androidBridgeStreamStop?.();
      // window?.AndroidInterface?.onTabSwitch?.(true);
    },
    onClose: () => {
      if (__DEV__)
        console.log('tempText', tempText);

      isConvertingText.value = false;
    },
    onOpen: () => {
    },
  })
    .finally(() => {
      isSpeechConverting.value = false;
    });
}

const chatMessageWithTabType = ref<{ [key in number]: { message: string; type: string }[] }>({});
function onReceiveAsrResult(text: string, isLoading: boolean) {
  console.log('onReceiveAsrResult', { text, isLoading });
  if (!text && isLoading) {
    const newMessage = {
      type: 'student',
      message: '',
    };
    if (!chatMessageWithTabType.value[activeTab.value]) {
      chatMessageWithTabType.value[activeTab.value] = [];
    }
    chatMessageWithTabType.value[activeTab.value].push(newMessage);
  }
  else if (text && !isLoading) {
    // debouncedReceiveAsrResult(text, isLoading);
    const chatMessage = chatMessageWithTabType.value[activeTab.value];
    chatMessage[chatMessage.length - 1].message = text;
    askQuestion(text);
  }
  else if (!text && !isLoading) {
    // 没说话，什么都没返回。
    const chatMessage = chatMessageWithTabType.value[activeTab.value];
    chatMessage.splice(chatMessage.length - 1, 1);
    isUsingMic.value = false;
  }
};
const debouncedReceiveAsrResult = useDebounceFn((text: string, isLoading: boolean) => {
  const chatMessage = chatMessageWithTabType.value[activeTab.value];
  chatMessage[chatMessage.length - 1].message = text;
  askQuestion(text);
}, 100);

let isAskingQuestion = false;
const isAnsweringQuestion = ref(false);
const isFetchingQA = ref(false);
function askQuestion(question: string) {
  if (isAskingQuestion)
    return;
  isAskingQuestion = true;
  const typeMapping: (ITabType)[] = ['knowledge_point_agent', 'understanding_agent', 'solution_steps_agent'];
  const tabType = typeMapping[activeTab.value];
  const newMessage = {
    type: 'ai',
    message: '',
  };
  chatMessageWithTabType.value[activeTab.value].push(newMessage);
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
  isAnsweringQuestion.value = true;
  isFetchingQA.value = true;
  const chatMessage = chatMessageWithTabType.value[activeTab.value];
  fetchEventData(`${import.meta.env.VITE_BASE_URL}/api/v1/math/qa2`, {
    // fetchEventData(`/customIP/api/v1/math/qa2`, {
    method: 'post',
    headers: {
      Authorization: authrizationToken,
    },
    data: {
      session_id: recordId,
      query: question,
      type: tabType,
    },
    onMessage: (e) => {
      if (!e || !e.data)
        return;

      try {
        const data = JSON.parse(e.data);
        if (__DEV__) {
          // console.log('QA 回答', data, data.content);
          console.log('QA 回答', data.content);
        }

        if (data?.session_id) {
          chatMessage[chatMessage.length - 1].message += data.content;
        }
        if (data?.thread_id && isPageVisible.value) {
          window?.AndroidInterface?.androidBridgeBroadcastByText?.(data.content);
        }
      }
      catch (error) {
        console.error('Failed to parse SSE answer message:', error);
      }
    },
    onError: () => {
      window?.AndroidInterface?.androidBridgeStreamStop?.();
      if (!isHistoryMode.value) {
        window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
      }
    },
    onClose: () => {
      window?.AndroidInterface?.androidBridgeStreamStop?.();
    },
  })
    .finally(() => {
      isAskingQuestion = false;
      isFetchingQA.value = false;
      isUsingMic.value = false;
    });
}

function androidBridgeAudioPlayFinished() {
  console.log('androidBridgeAudioPlayFinished');
  isSpeeching.value = false;
  isAnsweringQuestion.value = false;
  if (!isHistoryMode.value && activeTab.value !== 3) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('true');
  }
}

const presetPhraseList = ref([]);
function getPresetPhraseList() {
  if (presetPhraseList.value.length)
    return;

  request.get('/api/v1/math/config', {
    params: {
      key: 'h5',
    },
    headers: {
      Authorization: authrizationToken,
    },
  })
    .then((res) => {
      console.log('getPresetPhraseList', res.data);
      presetPhraseList.value = res.data?.awake_prompt || [];
    });
};
const isUsingMic = ref(false);
function androidBridgeOnMicClick() {
  // const tabMapping: { [key in number]: string[] } = {
  //   0: ['哪儿不懂咱一起探讨？', '哪些知识想深入聊聊？', '哪些地方需要老师一起看看？', '哪些困惑想现在解决呀？'],
  //   1: ['咱们有什么问题，详细说说', '哪儿不懂咱一起探讨？', '哪些知识想深入聊聊？', '哪些地方需要老师一起看看？', '哪些困惑想现在解决呀？'],
  //   2: ['哪儿不懂咱一起探讨？', '哪些知识想深入聊聊？', '哪些地方需要老师一起看看？', '哪些困惑想现在解决呀？'],
  // };
  // const understandingPhraseList = ['咱们有什么问题，详细说说', '哪儿不懂咱一起探讨？', '哪些知识想深入聊聊？', '哪些地方需要老师一起看看？', '哪些困惑想现在解决呀？'];
  // const solutionStepPhraseList = ['哪儿不懂咱一起探讨？', '哪些知识想深入聊聊？', '哪些地方需要老师一起看看？', '哪些困惑想现在解决呀？'];
  isUsingMic.value = true;
  const targetPhraseList = presetPhraseList.value;
  const randomIndex = Math.floor(Math.random() * targetPhraseList.length);
  const phrase = targetPhraseList[randomIndex] || '';
  const newMessage = {
    type: 'preset',
    message: phrase,
  };
  const chatMessage = chatMessageWithTabType.value[activeTab.value];
  if (chatMessage) {
    chatMessage.push(newMessage);
  }
  else {
    chatMessageWithTabType.value[activeTab.value] = [newMessage];
  }

  console.log('androidBridgeOnMicClick', phrase);
  return phrase;
}

const moveToBottom = useDebounceFn(() => {
  contentContainer.value!.scrollTo({
    top: contentContainer.value!.scrollHeight,
    behavior: 'smooth',
  });
}, 200);

function androidBridgePageClosed() {
  setTimeout(() => {
    console.log('androidBridgePageClosed');
    isPageVisible.value = false;
    window?.AndroidInterface?.androidBridgeStreamStop?.();
    window?.AndroidInterface?.androidBridgeClearTTS?.();
    androidBridgeAudioPlayFinished();
  }, 200);
}

function resetDefaultState() {
  if (isSpeechConverting.value) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    isSpeeching.value = true;
  }
  if (isFetchingQA.value) {
    window?.AndroidInterface?.androidBridgeShowRecordView?.('false');
    isAnsweringQuestion.value = true;
  }
}

onMounted(() => {
  window?.AndroidInterface?.androidBridgeShowRecordView?.('false');

  useMutationObserver(contentContainer.value, () => {
    moveToBottom();
  }, {
    // attributes: true,
    subtree: true,
    childList: true,
  });

  let lastPageVisibleValue = true;
  // 监听 webview 可见性。native 休眠时会触发该回调
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      console.log('webview可见');
      // if (isPageVisible.value) {
      // }
      // resetDefaultState();
      isPageVisible.value = lastPageVisibleValue;
    }
    else {
      console.log('webview不可见');
      lastPageVisibleValue = isPageVisible.value;
    }
  });
});

window.androidBridgeFetchKnowledge = androidBridgeFetchKnowledge;
window.androidBridgeFetchKnowledgeHistory = androidBridgeFetchKnowledgeHistory;
window.onReceiveAsrResult = onReceiveAsrResult;
window.androidBridgeAudioPlayFinished2 = androidBridgeAudioPlayFinished;
window.androidBridgeOnMicClick = androidBridgeOnMicClick;
window.androidBridgePageClosed = androidBridgePageClosed;
function refreshPage() {
  window.location.reload();
};
// androidBridgeFetchKnowledge({ index: 0, ocr_record_id: 'FBCEDE43-C29C-5697-9739-B4CFABFFC147', token: 'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsYXQiOjE3NTMxNzc0OTMsImV4cCI6MTc1Mzc4MjI5MywianRpIjoiYzk5NTVhNjctZjNjMC00NDUzLThmMmYtZDQ4YzZkZjBjM2I4IiwidHlwZSI6ImFjY2VzcyIsInN1YmplY3QiOnsiaWQiOjE0MH19.GtYKMHp53zxZ4NOOM97PMQVmEFfHT06RBdQPrlPX24U', type: 'understanding_agent' });
</script>

<style lang="scss" scoped>
.broadcast-btn {
  @apply fixed right-[30px] top-[76px] z-[100] translate-y-[-50%] flex justify-center items-center gap-x-[4px] px-[10px];
  background-image: url('@/assets/broadcast-bg.png');
  min-width: 72px;
  height: 32px;
  background-size: 100% 32px;

  .bg-text {
    @apply pr-[5px];
    font-size: 16px;
    // width: 32px;
    font-style: italic;
    font-weight: bold;
    background-image: linear-gradient(to bottom, #3e9dfb, #6356fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.custom-wave-class {
  :deep(.wave-loader) {
    padding: 0;
    width: 20px;
    height: 13px;
    gap: 0 2px;

    span {
      flex-shrink: 0;
      width: 1px;
      height: 13px;
    }
  }
}

.custom-md-previewer {
  @apply px-[20px];

  :deep() {
    .katex-html {
      white-space: break-spaces;
    }
  }
}

.loader {
  width: 12px;
  height: 12px;
  border: 3px solid #8b72ff;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
