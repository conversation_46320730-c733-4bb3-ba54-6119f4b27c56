import eruda from 'eruda';

import katex from 'katex';
import { config } from 'md-editor-v3';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import 'katex/dist/katex.min.css';
import 'virtual:uno.css';

if (__DEV__) {
  eruda.init();
}

config({
  editorExtensions: {
    katex: {
      instance: katex,
    },
  },
});

createApp(App).use(router).mount('#app');
